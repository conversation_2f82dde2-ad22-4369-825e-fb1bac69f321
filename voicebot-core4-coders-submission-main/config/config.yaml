# Configuration for the P2P Lending Voice Bot

# ==============================================================================
# AWS Bedrock Configuration
# ==============================================================================
aws_bedrock:
  region: "us-east-1" # Replace with your AWS region if different
  knowledge_base:
    model_id: "anthropic.claude-3-sonnet-20240229-v1:0" # Foundational model for knowledge retrieval
    max_results: 3 # Number of search results to retrieve from the knowledge base

  response_generation:
    model_id: "anthropic.claude-3-haiku-20240307-v1:0" # Cost-effective and fast model for generating responses
    temperature: 0.7
    max_tokens: 500

# ==============================================================================
# API Gateway Configuration
# ==============================================================================
api_gateway:
  # The base URL should be stored in an environment variable (API_GATEWAY_URL)
  # Example: https://xxxxxxxxxx.execute-api.us-east-1.amazonaws.com/dev
  base_url: "wss://jiehdal92f.execute-api.us-west-2.amazonaws.com/devx/"

  # The API key should be stored in an environment variable (API_GATEWAY_KEY)
  api_key: ""

  endpoints:
    nlp: "/nlp" # Endpoint for all NLP operations (intent, entities, knowledge base)

# ==============================================================================
# ASR (Automatic Speech Recognition) Configuration
# ==============================================================================
asr:
  model_id: scribe_v1 # ElevenLabs STT model
  languages: ["en", "hi"] # Support for English and Hindi languages

# ==============================================================================
# TTS (Text-to-Speech) Configuration
# ==============================================================================
tts:
  voice_id: EXAVITQu4vr4xnSDxMaL # Default voice for ElevenLabs
  model_id: eleven_v3 # Default model for ElevenLabs
  output_format: mp3 # For file saving, streaming is raw PCM
  speed: 1.0 # Not directly used by streaming, but kept for consistency
  languages: ["en", "hi"] # Support for English and Hindi languages

# ==============================================================================
# Logging Configuration
# ==============================================================================
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
