<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Speech-to-Speech Demo - P2P Lending Voice AI</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary: #4e75ff;
            --secondary: #5d6795;
            --background: #f5f7fa;
            --text: #333;
            --card-bg: #ffffff;
            --border: #e0e4ec;
            --highlight: #4e75ff;
            --success: #56bd5b;
            --error: #e74c3c;
            --shadow: rgba(0, 0, 0, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background);
            color: var(--text);
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        h1 {
            color: var(--primary);
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: var(--secondary);
            font-weight: normal;
        }
        
        .card {
            background: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 10px var(--shadow);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .card h2 {
            border-bottom: 1px solid var(--border);
            padding-bottom: 10px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .card h2 i {
            margin-right: 8px;
            color: var(--primary);
        }
        
        .demo-controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .record-container {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .record-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: white;
            border: 2px solid var(--primary);
            color: var(--primary);
            font-size: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        
        .record-button:hover {
            background: rgba(78, 117, 255, 0.1);
        }
        
        .record-button.recording {
            background: var(--error);
            border-color: var(--error);
            color: white;
            animation: pulse 1.5s infinite;
        }
        
        .status {
            flex: 1;
            font-size: 16px;
        }
        
        .upload-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .upload-container label {
            padding: 8px 16px;
            background: var(--primary);
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .upload-container label:hover {
            background: var(--highlight);
        }
        
        .upload-container input {
            display: none;
        }
        
        .file-name {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .voice-selector {
            margin-top: 20px;
            padding: 10px 0;
            border-top: 1px solid var(--border);
        }
        
        .voice-selector h3 {
            margin-bottom: 10px;
            color: var(--secondary);
        }
        
        .voice-selector select {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid var(--border);
            background-color: white;
        }
        
        .results {
            margin-top: 30px;
        }
        
        .result-box {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            background: #f9f9f9;
            border-left: 4px solid var(--primary);
        }
        
        .result-box h3 {
            font-size: 14px;
            color: var(--secondary);
            margin-bottom: 8px;
        }
        
        .audio-player {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        
        .audio-player button {
            padding: 8px 16px;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .audio-player button:hover {
            background: var(--highlight);
        }
        
        .error-message {
            color: var(--error);
            margin-top: 5px;
            font-size: 14px;
        }
        
        .loading-spinner {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--secondary);
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
            }
        }
        
        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: var(--primary);
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Speech-to-Speech Demo</h1>
            <p class="subtitle">Speak or upload audio to get a P2P Lending AI response</p>
        </header>
        
        <div class="card">
            <h2><i class="fas fa-microphone-alt"></i> Voice Input</h2>
            
            <div class="demo-controls">
                <div class="record-container">
                    <button id="recordButton" class="record-button">
                        <i id="recordIcon" class="fas fa-microphone"></i>
                    </button>
                    <div class="status">
                        <div id="recordingStatus">Click the microphone button to start recording</div>
                    </div>
                </div>
                
                <div class="upload-container">
                    <label for="fileUpload">
                        <i class="fas fa-upload"></i> Upload Audio
                    </label>
                    <input type="file" id="fileUpload" accept="audio/*" />
                    <div id="fileName" class="file-name">No file selected</div>
                </div>
                
                <div class="voice-selector">
                    <h3>Select Voice for Response</h3>
                    <select id="voiceSelector">
                        <option value="EXAVITQu4vr4xnSDxMaL">Rachel (Female)</option>
                        <option value="pNInz6obpgDQGcFmaJgB">Adam (Male)</option>
                        <option value="21m00Tcm4TlvDq8ikWAM">Antoni (Male)</option>
                        <option value="AZnzlk1XvdvUeBnXmlld">Domi (Female)</option>
                        <option value="IKne3meq5aSn9XLyUdCD">Callum (Male)</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="results" id="results">
            <!-- Results will be dynamically added here -->
        </div>
        
        <a href="/" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Main Demo
        </a>
    </div>
    
    <script>
        // JavaScript for the demo
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const recordButton = document.getElementById('recordButton');
            const recordIcon = document.getElementById('recordIcon');
            const statusText = document.getElementById('recordingStatus');
            const fileInput = document.getElementById('fileUpload');
            const fileName = document.getElementById('fileName');
            const voiceSelector = document.getElementById('voiceSelector');
            const resultsContainer = document.getElementById('results');
            
            // State
            let mediaRecorder;
            let audioChunks = [];
            let isRecording = false;
            let audioBlob;
            let audioUrl;
            let processingRequest = false;
            
            // Initialize microphone access
            function initMicrophone() {
                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(stream => {
                        mediaRecorder = new MediaRecorder(stream);
                        
                        mediaRecorder.ondataavailable = event => {
                            audioChunks.push(event.data);
                        };
                        
                        mediaRecorder.onstop = () => {
                            audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                            audioUrl = URL.createObjectURL(audioBlob);
                            audioChunks = [];
                            
                            statusText.textContent = 'Recording stopped. Ready to process.';
                            processAudio(audioBlob);
                        };
                        
                        statusText.textContent = 'Microphone ready. Click the button to start recording.';
                    })
                    .catch(error => {
                        console.error('Error accessing microphone:', error);
                        statusText.textContent = 'Error accessing microphone. Please check permissions.';
                        recordButton.disabled = true;
                    });
            }
            
            // Toggle recording
            function toggleRecording() {
                if (isRecording) {
                    mediaRecorder.stop();
                    isRecording = false;
                    recordButton.classList.remove('recording');
                    recordIcon.className = 'fas fa-microphone';
                    statusText.textContent = 'Processing recording...';
                } else {
                    audioChunks = [];
                    mediaRecorder.start();
                    isRecording = true;
                    recordButton.classList.add('recording');
                    recordIcon.className = 'fas fa-stop';
                    statusText.textContent = 'Recording... Click to stop.';
                }
            }
            
            // Process audio file (either recorded or uploaded)
            function processAudio(blob) {
                if (processingRequest) {
                    statusText.textContent = 'Already processing a request. Please wait.';
                    return;
                }
                
                processingRequest = true;
                statusText.textContent = 'Processing your speech...';
                
                // Create form data
                const formData = new FormData();
                formData.append('audio', blob);
                
                // Add selected voice ID if available
                const selectedVoice = voiceSelector.value;
                if (selectedVoice) {
                    formData.append('voice_id', selectedVoice);
                }
                
                // Create a result box immediately to show progress
                const resultId = Date.now();
                const resultBox = createResultBox(resultId, 'Processing...', 'Transcribing your speech...');
                resultsContainer.insertBefore(resultBox, resultsContainer.firstChild);
                
                // Send to server
                fetch('/api/speech-to-speech', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Server responded with status ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    processingRequest = false;
                    
                    // Update the result box with the transcription and response
                    updateResultBox(
                        resultId, 
                        data.text || 'No transcription available', 
                        data.response || 'No response available',
                        data.audio_url,
                        data.audio_status || 'ready'
                    );
                    
                    statusText.textContent = 'Processing complete!';
                    
                    // Poll for audio file if it's still generating
                    if (data.audio_status === 'generating' && data.audio_url) {
                        pollForAudioFile(resultId, data.audio_url);
                    }
                })
                .catch(error => {
                    processingRequest = false;
                    console.error('Error processing audio:', error);
                    statusText.textContent = 'Error processing audio. Please try again.';
                    
                    // Update the result box with the error
                    updateResultBox(
                        resultId, 
                        'Error', 
                        'Failed to process your speech. Please try again.',
                        null
                    );
                });
            }
            
            // Poll for audio file until it's available
            function pollForAudioFile(resultId, audioUrl, attempts = 0) {
                if (attempts > 20) { // Give up after 20 attempts (20 seconds)
                    console.log('Giving up on audio file polling');
                    return;
                }
                
                fetch(audioUrl, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            // Audio file is ready
                            const resultBox = document.getElementById(`result-${resultId}`);
                            if (resultBox) {
                                const audioPlayer = resultBox.querySelector('.audio-player');
                                if (audioPlayer) {
                                    // Update the audio player
                                    const loadingSpinner = audioPlayer.querySelector('.loading-spinner');
                                    if (loadingSpinner) {
                                        loadingSpinner.remove();
                                    }
                                    
                                    const playButton = document.createElement('button');
                                    playButton.innerHTML = '<i class="fas fa-play"></i> Play Response';
                                    playButton.onclick = () => playAudio(audioUrl);
                                    audioPlayer.appendChild(playButton);
                                }
                            }
                        } else {
                            // Audio file not ready yet, poll again after a delay
                            setTimeout(() => pollForAudioFile(resultId, audioUrl, attempts + 1), 1000);
                        }
                    })
                    .catch(error => {
                        console.log('Error checking audio file:', error);
                        // Try again after a delay
                        setTimeout(() => pollForAudioFile(resultId, audioUrl, attempts + 1), 1000);
                    });
            }
            
            // Create a result box
            function createResultBox(id, transcription, response) {
                const box = document.createElement('div');
                box.className = 'result-box';
                box.id = `result-${id}`;
                
                box.innerHTML = `
                    <h3>Transcription:</h3>
                    <div class="transcription">${transcription}</div>
                    <h3>Response:</h3>
                    <div class="response">${response}</div>
                    <div class="audio-player">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i> Generating audio...
                        </div>
                    </div>
                `;
                
                return box;
            }
            
            // Update a result box
            function updateResultBox(id, transcription, response, audioUrl, audioStatus) {
                const resultBox = document.getElementById(`result-${id}`);
                if (!resultBox) return;
                
                // Update transcription and response
                resultBox.querySelector('.transcription').textContent = transcription;
                resultBox.querySelector('.response').textContent = response;
                
                // Update audio player
                const audioPlayer = resultBox.querySelector('.audio-player');
                
                // Clear existing content
                if (audioUrl) {
                    if (audioStatus === 'ready') {
                        // Audio is ready to play
                        audioPlayer.innerHTML = '';
                        const playButton = document.createElement('button');
                        playButton.innerHTML = '<i class="fas fa-play"></i> Play Response';
                        playButton.onclick = () => playAudio(audioUrl);
                        audioPlayer.appendChild(playButton);
                    } else {
                        // Audio is still generating
                        audioPlayer.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Generating audio...</div>';
                    }
                } else {
                    // No audio available
                    audioPlayer.innerHTML = '<div class="error-message">Audio not available</div>';
                }
            }
            
            // Play audio
            function playAudio(url) {
                const audio = new Audio(url);
                audio.play();
            }
            
            // Handle file upload
            fileInput.addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    const file = this.files[0];
                    fileName.textContent = file.name;
                    
                    // Convert the file to a blob and process it
                    const reader = new FileReader();
                    reader.onload = function(event) {
                        const arrayBuffer = event.target.result;
                        const blob = new Blob([arrayBuffer], { type: file.type });
                        processAudio(blob);
                    };
                    reader.readAsArrayBuffer(file);
                }
            });
            
            // Record button click handler
            recordButton.addEventListener('click', toggleRecording);
            
            // Initialize microphone on page load
            initMicrophone();
        });
    </script>
</body>
</html> 