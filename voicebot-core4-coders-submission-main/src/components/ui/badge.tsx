import React from "react";

interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "secondary" | "destructive" | "outline";
}

export const Badge: React.FC<BadgeProps> = ({
  variant = "default",
  className = "",
  ...props
}) => {
  const baseClasses = "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2";
  
  const variantClasses = {
    default: "border-transparent bg-blue-500 text-white",
    secondary: "border-transparent bg-purple-500 text-white",
    destructive: "border-transparent bg-red-500/20 text-red-300 border-red-500/30",
    outline: "border-white/20 text-white",
  };
  
  const classes = `${baseClasses} ${variantClasses[variant]} ${className}`;
  
  return (
    <div className={classes} {...props} />
  );
};
