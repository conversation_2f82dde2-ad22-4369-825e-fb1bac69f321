{"name": "voicebot-core4-coders-submission", "version": "1.0.0", "description": "A state-of-the-art voice AI assistant designed to educate potential users about Peer-to-Peer lending and guide them through the initial sales and onboarding process. This application uses advanced speech recognition and synthesis capabilities to create natural, human-like conversation flows.", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git@github-mastercode:aircmastercode/voicebot-core4-coders-submission.git"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.5", "tailwindcss": "^4.1.10", "typescript": "^5.4.5", "vite": "^5.2.6"}, "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.363.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1"}}